#!/usr/bin/env python3
"""
Optimized Monthly Aggregated Tract Flow Analysis Pipeline

This optimized version processes monthly aggregation more efficiently by:
1. Processing files in smaller chunks
2. Using more efficient data structures
3. Optimizing the visitor_home_cbgs merging process

Author: Augment Agent
Date: 2024
"""

import pandas as pd
import numpy as np
import re
import json
import os
import glob
import gzip
from datetime import datetime
from collections import defaultdict
import subprocess
import sys

def analyze_weekly_files():
    """Analyze weekly files and group them by month"""
    print("🔍 Analyzing weekly files and grouping by month...")
    
    # Get all weekly files
    weekly_files = glob.glob('NYC-2024-weekly-20250509T045249Z-1-002/NYC-2024-weekly/*.csv.gz')
    weekly_files = [f for f in weekly_files if 'DATE_RANGE_START' in f]  # Exclude X.csv
    
    print(f"   Found {len(weekly_files)} weekly files")
    
    # Extract dates and group by month
    monthly_groups = defaultdict(list)
    date_pattern = r'DATE_RANGE_START_(\d{4}-\d{2}-\d{2})'
    
    for file_path in weekly_files:
        filename = os.path.basename(file_path)
        match = re.search(date_pattern, filename)
        if match:
            date_str = match.group(1)
            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
            month_key = f"{date_obj.year}-{date_obj.month:02d}"
            monthly_groups[month_key].append({
                'file': file_path,
                'date': date_str,
                'month': month_key
            })
    
    # Sort files within each month by date
    for month in monthly_groups:
        monthly_groups[month].sort(key=lambda x: x['date'])
    
    print(f"\n📅 Monthly groupings:")
    for month in sorted(monthly_groups.keys()):
        files = monthly_groups[month]
        print(f"   {month}: {len(files)} files")
        for file_info in files:
            print(f"      - {file_info['date']}")
    
    return monthly_groups

def parse_visitor_home_cbgs_fast(json_str):
    """Fast parsing of visitor_home_cbgs"""
    if pd.isna(json_str) or json_str == '{}' or json_str == '':
        return {}

    try:
        # Use regex to extract CBG and count pairs
        pattern = r'""""(\d+)"""":([\d]+)'
        matches = re.findall(pattern, str(json_str))
        
        result = {}
        for cbg, count in matches:
            result[cbg] = int(count)
        return result
    except:
        return {}

def aggregate_monthly_data_optimized(monthly_groups):
    """Optimized aggregation of weekly data into monthly files"""
    print("\n🔄 Aggregating weekly data into monthly files (optimized)...")
    
    # Create monthly aggregated directory
    monthly_dir = 'NYC-2024-monthly-aggregated'
    os.makedirs(monthly_dir, exist_ok=True)
    
    monthly_files = []
    
    for month in sorted(monthly_groups.keys()):
        print(f"\n📊 Processing month: {month}")
        files = monthly_groups[month]
        
        # Use a dictionary to accumulate data by placekey
        placekey_data = {}
        total_rows = 0
        
        for file_info in files:
            print(f"   📖 Reading {os.path.basename(file_info['file'])}...")
            try:
                with gzip.open(file_info['file'], 'rt', encoding='utf-8', errors='replace') as f:
                    # Read in chunks to manage memory
                    chunk_size = 10000
                    for chunk in pd.read_csv(f, chunksize=chunk_size):
                        total_rows += len(chunk)
                        
                        # Process each row in the chunk
                        for _, row in chunk.iterrows():
                            placekey = row['placekey']
                            
                            if placekey not in placekey_data:
                                # Initialize with first occurrence
                                placekey_data[placekey] = {
                                    'placekey': placekey,
                                    'poi_cbg': row['poi_cbg'],
                                    'raw_visit_counts': 0,
                                    'latitude': row['latitude'],
                                    'longitude': row['longitude'],
                                    'visitor_home_cbgs': {},
                                    'other_columns': {}
                                }
                                
                                # Store other columns from first occurrence
                                for col in chunk.columns:
                                    if col not in ['placekey', 'poi_cbg', 'raw_visit_counts', 
                                                 'latitude', 'longitude', 'visitor_home_cbgs']:
                                        placekey_data[placekey]['other_columns'][col] = row[col]
                            
                            # Accumulate raw_visit_counts
                            placekey_data[placekey]['raw_visit_counts'] += row['raw_visit_counts']
                            
                            # Parse and merge visitor_home_cbgs
                            cbgs = parse_visitor_home_cbgs_fast(row['visitor_home_cbgs'])
                            for cbg, count in cbgs.items():
                                if cbg in placekey_data[placekey]['visitor_home_cbgs']:
                                    placekey_data[placekey]['visitor_home_cbgs'][cbg] += count
                                else:
                                    placekey_data[placekey]['visitor_home_cbgs'][cbg] = count
                
                print(f"      Processed {total_rows} rows so far...")
                        
            except Exception as e:
                print(f"      ❌ Error reading file: {e}")
                continue
        
        if not placekey_data:
            print(f"   ❌ No data loaded for month {month}")
            continue
        
        print(f"   🎯 Creating aggregated DataFrame...")
        
        # Convert accumulated data to DataFrame
        aggregated_data = []
        for placekey, data in placekey_data.items():
            # Format visitor_home_cbgs back to original format
            if data['visitor_home_cbgs']:
                formatted_pairs = [f'""""{cbg}"""":{count}' for cbg, count in data['visitor_home_cbgs'].items()]
                visitor_home_cbgs_str = '{' + ','.join(formatted_pairs) + '}'
            else:
                visitor_home_cbgs_str = '{}'
            
            row_data = {
                'placekey': data['placekey'],
                'poi_cbg': data['poi_cbg'],
                'visitor_home_cbgs': visitor_home_cbgs_str,
                'raw_visit_counts': data['raw_visit_counts'],
                'latitude': data['latitude'],
                'longitude': data['longitude']
            }
            
            # Add other columns
            row_data.update(data['other_columns'])
            aggregated_data.append(row_data)
        
        # Create DataFrame
        aggregated_df = pd.DataFrame(aggregated_data)
        
        print(f"   ✅ Aggregated to {len(aggregated_df)} unique placekeys")
        print(f"   📉 Reduction: {total_rows:,} → {len(aggregated_df):,} rows ({len(aggregated_df)/total_rows*100:.1f}%)")
        
        # Save monthly aggregated file
        month_num = month.split('-')[1]
        output_filename = f"NYC-2024-monthly-{month_num}.csv.gz"
        output_path = os.path.join(monthly_dir, output_filename)
        
        print(f"   💾 Saving to {output_filename}...")
        with gzip.open(output_path, 'wt', encoding='utf-8') as f:
            aggregated_df.to_csv(f, index=False)
        
        monthly_files.append({
            'month': month,
            'file': output_path,
            'rows': len(aggregated_df),
            'original_rows': total_rows
        })
        
        print(f"   ✅ Saved {output_filename}")
        
        # Clear memory
        del placekey_data
        del aggregated_data
        del aggregated_df
    
    print(f"\n📋 Monthly aggregation summary:")
    total_original = 0
    total_aggregated = 0
    for file_info in monthly_files:
        print(f"   {file_info['month']}: {file_info['original_rows']:,} → {file_info['rows']:,} rows")
        total_original += file_info['original_rows']
        total_aggregated += file_info['rows']
    
    print(f"\n📊 Overall aggregation:")
    print(f"   Original total: {total_original:,} rows")
    print(f"   Aggregated total: {total_aggregated:,} rows")
    print(f"   Reduction: {(1 - total_aggregated/total_original)*100:.1f}%")
    
    return monthly_files

def main():
    """Main pipeline execution"""
    print("🚀 OPTIMIZED MONTHLY AGGREGATED TRACT FLOW ANALYSIS PIPELINE")
    print("=" * 70)
    
    # Step 1: Analyze and group weekly files
    monthly_groups = analyze_weekly_files()
    
    if not monthly_groups:
        print("❌ No weekly files found!")
        return False
    
    # Step 2: Aggregate weekly data into monthly files (optimized)
    monthly_files = aggregate_monthly_data_optimized(monthly_groups)
    
    if not monthly_files:
        print("❌ No monthly files created!")
        return False
    
    print(f"\n✅ STEP 1 COMPLETED: Monthly Input File Aggregation")
    print(f"   Created {len(monthly_files)} monthly aggregated input files")
    print(f"   Files saved in: NYC-2024-monthly-aggregated/")
    
    print(f"\n📋 Next steps:")
    print(f"   1. ✅ Monthly input aggregation completed")
    print(f"   2. 🔄 Run modified process_tract_flows.py on monthly files")
    print(f"   3. 📊 Generate monthly tract flow output files")
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n🎉 Optimized monthly aggregation pipeline completed successfully!")
    else:
        print(f"\n❌ Pipeline failed!")
        sys.exit(1)
