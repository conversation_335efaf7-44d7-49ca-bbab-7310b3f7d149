#!/usr/bin/env python3
"""
Monthly Aggregated Tract Flow Analysis Pipeline

This script creates a complete pipeline for monthly tract flow analysis:
1. Groups weekly CSV.gz files by month
2. Aggregates data within each month (sum raw_visit_counts, merge visitor_home_cbgs)
3. Processes monthly aggregated data through the tract flow analysis pipeline
4. Generates monthly tract flow output files

Author: Augment Agent
Date: 2024
"""

import pandas as pd
import numpy as np
import re
import json
import os
import glob
import gzip
from datetime import datetime
from collections import defaultdict
import subprocess
import sys

def analyze_weekly_files():
    """Analyze weekly files and group them by month"""
    print("🔍 Analyzing weekly files and grouping by month...")
    
    # Get all weekly files
    weekly_files = glob.glob('NYC-2024-weekly-20250509T045249Z-1-002/NYC-2024-weekly/*.csv.gz')
    weekly_files = [f for f in weekly_files if 'DATE_RANGE_START' in f]  # Exclude X.csv
    
    print(f"   Found {len(weekly_files)} weekly files")
    
    # Extract dates and group by month
    monthly_groups = defaultdict(list)
    date_pattern = r'DATE_RANGE_START_(\d{4}-\d{2}-\d{2})'
    
    for file_path in weekly_files:
        filename = os.path.basename(file_path)
        match = re.search(date_pattern, filename)
        if match:
            date_str = match.group(1)
            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
            month_key = f"{date_obj.year}-{date_obj.month:02d}"
            monthly_groups[month_key].append({
                'file': file_path,
                'date': date_str,
                'month': month_key
            })
    
    # Sort files within each month by date
    for month in monthly_groups:
        monthly_groups[month].sort(key=lambda x: x['date'])
    
    print(f"\n📅 Monthly groupings:")
    for month in sorted(monthly_groups.keys()):
        files = monthly_groups[month]
        print(f"   {month}: {len(files)} files")
        for file_info in files:
            print(f"      - {file_info['date']}")
    
    return monthly_groups

def parse_visitor_home_cbgs(json_str):
    """Parse visitor_home_cbgs with extra quotes"""
    if pd.isna(json_str) or json_str == '{}':
        return {}

    # Use regex to extract CBG and count pairs
    pattern = r'""""(\d+)"""":([\d]+)'
    matches = re.findall(pattern, str(json_str))

    result = {}
    for cbg, count in matches:
        result[cbg] = int(count)

    return result

def merge_visitor_home_cbgs(cbg_dicts):
    """Merge multiple visitor_home_cbgs dictionaries by summing counts"""
    merged = {}
    for cbg_dict in cbg_dicts:
        if isinstance(cbg_dict, dict):
            for cbg, count in cbg_dict.items():
                merged[cbg] = merged.get(cbg, 0) + count
    
    # Convert back to the original format
    if not merged:
        return '{}'
    
    # Create the formatted string with extra quotes
    formatted_pairs = [f'""""{cbg}"""":{count}' for cbg, count in merged.items()]
    return '{' + ','.join(formatted_pairs) + '}'

def aggregate_monthly_data(monthly_groups):
    """Aggregate weekly data into monthly files"""
    print("\n🔄 Aggregating weekly data into monthly files...")
    
    # Create monthly aggregated directory
    monthly_dir = 'NYC-2024-monthly-aggregated'
    os.makedirs(monthly_dir, exist_ok=True)
    
    monthly_files = []
    
    for month in sorted(monthly_groups.keys()):
        print(f"\n📊 Processing month: {month}")
        files = monthly_groups[month]
        
        # Read and combine all weekly files for this month
        monthly_data = []
        total_rows = 0
        
        for file_info in files:
            print(f"   📖 Reading {os.path.basename(file_info['file'])}...")
            try:
                with gzip.open(file_info['file'], 'rt', encoding='utf-8', errors='replace') as f:
                    df = pd.read_csv(f)
                    monthly_data.append(df)
                    total_rows += len(df)
                    print(f"      Loaded {len(df)} rows")
            except Exception as e:
                print(f"      ❌ Error reading file: {e}")
                continue
        
        if not monthly_data:
            print(f"   ❌ No data loaded for month {month}")
            continue
        
        # Combine all weekly data
        print(f"   🔗 Combining {len(monthly_data)} weekly files...")
        combined_df = pd.concat(monthly_data, ignore_index=True)
        print(f"   📈 Combined data: {len(combined_df)} rows")
        
        # Aggregate by placekey
        print(f"   🎯 Aggregating by placekey...")
        
        # Parse visitor_home_cbgs for aggregation
        combined_df['parsed_visitor_home_cbgs'] = combined_df['visitor_home_cbgs'].apply(parse_visitor_home_cbgs)
        
        # Group by placekey and aggregate
        aggregated_data = []
        grouped = combined_df.groupby('placekey')
        
        for placekey, group in grouped:
            # Sum raw_visit_counts
            total_visits = group['raw_visit_counts'].sum()
            
            # Merge visitor_home_cbgs
            cbg_dicts = group['parsed_visitor_home_cbgs'].tolist()
            merged_cbgs = merge_visitor_home_cbgs(cbg_dicts)
            
            # Take first occurrence for other fields
            first_row = group.iloc[0]
            
            aggregated_row = {
                'placekey': placekey,
                'poi_cbg': first_row['poi_cbg'],
                'visitor_home_cbgs': merged_cbgs,
                'raw_visit_counts': total_visits,
                'latitude': first_row['latitude'],
                'longitude': first_row['longitude']
            }
            
            # Add any other columns that exist
            for col in combined_df.columns:
                if col not in aggregated_row and col != 'parsed_visitor_home_cbgs':
                    aggregated_row[col] = first_row[col]
            
            aggregated_data.append(aggregated_row)
        
        # Create aggregated DataFrame
        aggregated_df = pd.DataFrame(aggregated_data)
        
        # Remove the temporary parsed column
        if 'parsed_visitor_home_cbgs' in aggregated_df.columns:
            aggregated_df = aggregated_df.drop('parsed_visitor_home_cbgs', axis=1)
        
        print(f"   ✅ Aggregated to {len(aggregated_df)} unique placekeys")
        print(f"   📉 Reduction: {total_rows} → {len(aggregated_df)} rows ({len(aggregated_df)/total_rows*100:.1f}%)")
        
        # Save monthly aggregated file
        month_num = month.split('-')[1]
        output_filename = f"NYC-2024-monthly-{month_num}.csv.gz"
        output_path = os.path.join(monthly_dir, output_filename)
        
        print(f"   💾 Saving to {output_filename}...")
        with gzip.open(output_path, 'wt', encoding='utf-8') as f:
            aggregated_df.to_csv(f, index=False)
        
        monthly_files.append({
            'month': month,
            'file': output_path,
            'rows': len(aggregated_df),
            'original_rows': total_rows
        })
        
        print(f"   ✅ Saved {output_filename}")
    
    print(f"\n📋 Monthly aggregation summary:")
    total_original = 0
    total_aggregated = 0
    for file_info in monthly_files:
        print(f"   {file_info['month']}: {file_info['original_rows']} → {file_info['rows']} rows")
        total_original += file_info['original_rows']
        total_aggregated += file_info['rows']
    
    print(f"\n📊 Overall aggregation:")
    print(f"   Original total: {total_original:,} rows")
    print(f"   Aggregated total: {total_aggregated:,} rows")
    print(f"   Reduction: {(1 - total_aggregated/total_original)*100:.1f}%")
    
    return monthly_files

def main():
    """Main pipeline execution"""
    print("🚀 MONTHLY AGGREGATED TRACT FLOW ANALYSIS PIPELINE")
    print("=" * 60)
    
    # Step 1: Analyze and group weekly files
    monthly_groups = analyze_weekly_files()
    
    if not monthly_groups:
        print("❌ No weekly files found!")
        return False
    
    # Step 2: Aggregate weekly data into monthly files
    monthly_files = aggregate_monthly_data(monthly_groups)
    
    if not monthly_files:
        print("❌ No monthly files created!")
        return False
    
    print(f"\n✅ STEP 1 COMPLETED: Monthly Input File Aggregation")
    print(f"   Created {len(monthly_files)} monthly aggregated input files")
    print(f"   Files saved in: NYC-2024-monthly-aggregated/")
    
    print(f"\n📋 Next steps:")
    print(f"   1. ✅ Monthly input aggregation completed")
    print(f"   2. 🔄 Run modified process_tract_flows.py on monthly files")
    print(f"   3. 📊 Generate monthly tract flow output files")
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n🎉 Monthly aggregation pipeline completed successfully!")
    else:
        print(f"\n❌ Pipeline failed!")
        sys.exit(1)
