import os
import pandas as pd
import glob
import re
from datetime import datetime
from collections import defaultdict

def aggregate_csv_files_by_month():
    """
    Aggregates CSV files from the output_files directory by month.
    Creates a monthly_aggregated_data directory and saves one CSV file per month.
    """
    print("Starting monthly CSV aggregation process...")
    
    # Path to the output_files directory
    output_dir = 'output_files'
    
    # Path to the monthly_aggregated_data directory
    monthly_dir = 'monthly_aggregated_data'
    
    # Create the monthly_aggregated_data directory if it doesn't exist
    if not os.path.exists(monthly_dir):
        os.makedirs(monthly_dir)
        print(f"Created directory: {monthly_dir}")
    
    # Get a list of all CSV files in the output_files directory
    csv_files = glob.glob(os.path.join(output_dir, '*.csv'))
    
    if not csv_files:
        print("No CSV files found in the output_files directory.")
        return
    
    print(f"Found {len(csv_files)} CSV files to aggregate.")
    
    # Group files by month
    files_by_month = defaultdict(list)
    
    # Regular expression to extract date from filename
    date_pattern = re.compile(r'_(\d{4}-\d{2}-\d{2})\.csv$')
    
    for file in csv_files:
        filename = os.path.basename(file)
        match = date_pattern.search(filename)
        
        if match:
            date_str = match.group(1)
            try:
                # Parse the date
                date = datetime.strptime(date_str, '%Y-%m-%d')
                # Create a month key in format YYYY-MM
                month_key = date.strftime('%Y-%m')
                # Add file to the corresponding month
                files_by_month[month_key].append(file)
                print(f"Assigned {filename} to month {month_key}")
            except ValueError as e:
                print(f"Error parsing date from {filename}: {str(e)}")
        else:
            print(f"Could not extract date from filename: {filename}")
    
    # Process each month
    for month_key, files in files_by_month.items():
        print(f"\nProcessing month: {month_key}")
        print(f"Files to aggregate: {len(files)}")
        
        # Create an empty list to store dataframes for this month
        month_dfs = []
        
        # Read each CSV file for this month
        for i, file in enumerate(files):
            filename = os.path.basename(file)
            print(f"  Processing file {i+1}/{len(files)}: {filename}")
            
            try:
                # Read the CSV file
                df = pd.read_csv(file)
                
                # Remove 'Source_File' column if it exists
                if 'Source_File' in df.columns:
                    df = df.drop(columns=['Source_File'])
                
                # Append to the list
                month_dfs.append(df)
                
                print(f"    - Added {len(df)} rows from {filename}")
            except Exception as e:
                print(f"    - Error processing {file}: {str(e)}")
        
        if not month_dfs:
            print(f"  No data was successfully read for month {month_key}.")
            continue
        
        # Combine all dataframes for this month
        print(f"  Combining all dataframes for month {month_key}...")
        combined_df = pd.concat(month_dfs, ignore_index=True)
        
        # Save the combined dataframe to a new CSV file
        output_file = os.path.join(monthly_dir, f'tract_flows_{month_key}.csv')
        print(f"  Saving combined data to {output_file}...")
        combined_df.to_csv(output_file, index=False)
        
        print(f"  Month {month_key} aggregation complete. Total rows: {len(combined_df)}")
    
    print("\nMonthly aggregation process completed.")

if __name__ == "__main__":
    aggregate_csv_files_by_month()
