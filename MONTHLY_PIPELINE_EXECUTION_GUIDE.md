# Monthly Aggregated Tract Flow Analysis Pipeline - Execution Guide

## 🎯 **Overview**
This guide provides step-by-step instructions for executing the complete Monthly Aggregated Tract Flow Analysis Pipeline, which generates monthly tract flow files with enhanced coordinate accuracy and destination source tracking.

---

## 📋 **Prerequisites Check**

### **1. Verify Required Input Files**
Before starting, ensure all required files are present:

```bash
# Check weekly input files (should be 22 files)
ls -la "NYC-2024-weekly-20250509T045249Z-1-002/NYC-2024-weekly/"*.csv.gz | wc -l

# Check coordinate files
ls -la "2020_Gaz_tracts_national.txt"
ls -la "population files/census-populations-2020-tract-new-york.csv"
ls -la "population files/tessellation_with_population.geojson"

# Check pipeline scripts
ls -la "create_monthly_aggregated_optimized.py"
ls -la "process_monthly_tract_flows.py"
```

**Expected Results:**
- ✅ 22 weekly CSV.gz files in NYC-2024-weekly directory
- ✅ Gazetteer coordinate file (2020_Gaz_tracts_national.txt)
- ✅ Census population file (~5,411 tracts)
- ✅ Tessellation GeoJSON file (~1.2GB)
- ✅ Monthly aggregation script
- ✅ Monthly tract flow processor

### **2. Verify Python Dependencies**
```bash
# Check required Python packages
python -c "import pandas, numpy, json, gzip, glob, re; print('✅ All dependencies available')"
```

### **3. Check Available Disk Space**
```bash
# Check available space (need ~5-10GB for processing)
df -h .
```

---

## 🚀 **Step-by-Step Execution**

### **STEP 1: Monthly Input File Aggregation**

**Command:**
```bash
python create_monthly_aggregated_optimized.py > monthly_aggregation.log 2>&1
```

**What it does:**
- Groups 22 weekly files by month (8 months total)
- Aggregates data by placekey within each month
- Sums raw_visit_counts and merges visitor_home_cbgs
- Creates monthly aggregated input files

**Expected Processing Time:** 2-4 hours (depending on system performance)

**Expected Output:**
- Directory: `NYC-2024-monthly-aggregated/`
- Files: 8 monthly CSV.gz files
  - `NYC-2024-monthly-01.csv.gz` (January - 5 weekly files)
  - `NYC-2024-monthly-02.csv.gz` (February - 3 weekly files)
  - `NYC-2024-monthly-03.csv.gz` (March - 4 weekly files)
  - `NYC-2024-monthly-04.csv.gz` (April - 5 weekly files)
  - `NYC-2024-monthly-05.csv.gz` (May - 1 weekly file)
  - `NYC-2024-monthly-06.csv.gz` (June - 1 weekly file)
  - `NYC-2024-monthly-08.csv.gz` (August - 1 weekly file)
  - `NYC-2024-monthly-11.csv.gz` (November - 2 weekly files)

**Validation:**
```bash
# Check if monthly files were created
ls -la NYC-2024-monthly-aggregated/
echo "Monthly files created: $(ls NYC-2024-monthly-aggregated/*.csv.gz | wc -l)"

# Check file sizes (should be substantial)
du -h NYC-2024-monthly-aggregated/

# Check aggregation log for completion
tail -20 monthly_aggregation.log
```

**Success Indicators:**
- ✅ 8 monthly CSV.gz files created
- ✅ Significant data reduction (60-80% fewer rows than combined weekly data)
- ✅ Log shows "Monthly aggregation pipeline completed successfully!"

---

### **STEP 2: Monthly Tract Flow Processing**

**Command:**
```bash
python process_monthly_tract_flows.py > monthly_tract_flow_processing.log 2>&1
```

**What it does:**
- Loads coordinate data using updated 3-tier hierarchy (census → gazetteer → tessellation)
- Processes each monthly aggregated file through complete tract flow analysis
- Applies multi-polygon centroid calculation for tessellation data
- Tracks both source and destination coordinate sources
- Generates monthly tract flow output files

**Expected Processing Time:** 3-6 hours (due to large tessellation file loading)

**Expected Output:**
- Directory: `monthly_output_files/`
- Files: 8 monthly tract flow CSV files
  - `tract_flows_with_population_2024-month-01.csv`
  - `tract_flows_with_population_2024-month-02.csv`
  - `tract_flows_with_population_2024-month-03.csv`
  - `tract_flows_with_population_2024-month-04.csv`
  - `tract_flows_with_population_2024-month-05.csv`
  - `tract_flows_with_population_2024-month-06.csv`
  - `tract_flows_with_population_2024-month-08.csv`
  - `tract_flows_with_population_2024-month-11.csv`

**Validation:**
```bash
# Check if output files were created
ls -la monthly_output_files/
echo "Monthly tract flow files created: $(ls monthly_output_files/*.csv | wc -l)"

# Check file structure (should have 15 columns)
head -1 monthly_output_files/tract_flows_with_population_2024-month-01.csv

# Check processing log for completion
tail -20 monthly_tract_flow_processing.log
```

**Success Indicators:**
- ✅ 8 monthly tract flow CSV files created
- ✅ Each file has 15 columns including `Destination_Coordinate_Source`
- ✅ Files contain tract pairs with aggregated monthly flow data
- ✅ Log shows coordinate source statistics and successful completion

---

## 📊 **Expected Final Results**

### **File Structure:**
```
📂 Project Root
├── 📁 NYC-2024-monthly-aggregated/           # Monthly input files
│   ├── 📄 NYC-2024-monthly-01.csv.gz        # January aggregated data
│   ├── 📄 NYC-2024-monthly-02.csv.gz        # February aggregated data
│   ├── 📄 NYC-2024-monthly-03.csv.gz        # March aggregated data
│   ├── 📄 NYC-2024-monthly-04.csv.gz        # April aggregated data
│   ├── 📄 NYC-2024-monthly-05.csv.gz        # May aggregated data
│   ├── 📄 NYC-2024-monthly-06.csv.gz        # June aggregated data
│   ├── 📄 NYC-2024-monthly-08.csv.gz        # August aggregated data
│   └── 📄 NYC-2024-monthly-11.csv.gz        # November aggregated data
└── 📁 monthly_output_files/                  # Monthly tract flow outputs
    ├── 📄 tract_flows_with_population_2024-month-01.csv
    ├── 📄 tract_flows_with_population_2024-month-02.csv
    ├── 📄 tract_flows_with_population_2024-month-03.csv
    ├── 📄 tract_flows_with_population_2024-month-04.csv
    ├── 📄 tract_flows_with_population_2024-month-05.csv
    ├── 📄 tract_flows_with_population_2024-month-06.csv
    ├── 📄 tract_flows_with_population_2024-month-08.csv
    └── 📄 tract_flows_with_population_2024-month-11.csv
```

### **Output File Columns (15 total):**
1. `Source` - Source tract ID
2. `Destination` - Destination tract ID
3. `AggFlow` - Aggregated monthly flow value
4. `Buffer` - Number of tracts within buffer circle
5. `Source_Destination_Distance` - Distance between source and destination (km)
6. `Source_Latitude` - Source tract latitude
7. `Source_Longitude` - Source tract longitude
8. `Destination_Latitude` - Destination tract latitude
9. `Destination_Longitude` - Destination tract longitude
10. `Source_Population` - Source tract population
11. `Destination_Population` - Destination tract population
12. `Population_within_Circle` - Total population within buffer circle
13. `Missing_Population_Tracts` - Count of tracts missing population data
14. `Source_Coordinate_Source` - Source coordinate data source
15. `Destination_Coordinate_Source` - Destination coordinate data source (**NEW**)

---

## ⚠️ **Troubleshooting Guide**

### **Common Issues and Solutions:**

#### **Issue 1: "No weekly files found"**
**Solution:**
```bash
# Verify weekly files exist and have correct naming
ls -la "NYC-2024-weekly-20250509T045249Z-1-002/NYC-2024-weekly/"*DATE_RANGE_START*.csv.gz
```

#### **Issue 2: "Memory error during aggregation"**
**Solution:**
- Close other applications to free memory
- The optimized script uses chunked processing to minimize memory usage
- If still failing, consider processing months individually

#### **Issue 3: "Tessellation file not found or corrupted"**
**Solution:**
```bash
# Check tessellation file
ls -la "population files/tessellation_with_population.geojson"
# File should be ~1.2GB
```

#### **Issue 4: "Processing taking too long"**
**Expected behavior:**
- Monthly aggregation: 2-4 hours
- Tract flow processing: 3-6 hours
- Total pipeline: 5-10 hours

**Monitoring progress:**
```bash
# Monitor aggregation progress
tail -f monthly_aggregation.log

# Monitor tract flow processing progress
tail -f monthly_tract_flow_processing.log
```

#### **Issue 5: "Coordinate source statistics seem wrong"**
**Expected coordinate hierarchy:**
- Census: ~5,411 tracts (highest priority)
- Gazetteer: ~85,000+ tracts (medium priority)
- Tessellation: Variable (lowest priority)
- Not found: Remaining tracts

---

## ✅ **Final Validation**

### **Complete Pipeline Validation:**
```bash
# 1. Check all monthly input files created
echo "Monthly input files: $(ls NYC-2024-monthly-aggregated/*.csv.gz 2>/dev/null | wc -l)/8"

# 2. Check all monthly output files created
echo "Monthly output files: $(ls monthly_output_files/*.csv 2>/dev/null | wc -l)/8"

# 3. Verify output file structure
echo "Checking output file structure..."
head -1 monthly_output_files/tract_flows_with_population_2024-month-01.csv | tr ',' '\n' | nl

# 4. Check for destination coordinate source column
grep -q "Destination_Coordinate_Source" monthly_output_files/tract_flows_with_population_2024-month-01.csv && echo "✅ Destination tracking present" || echo "❌ Destination tracking missing"

# 5. Sample coordinate source distribution
echo "Sample coordinate source distribution:"
tail -n +2 monthly_output_files/tract_flows_with_population_2024-month-01.csv | cut -d',' -f14 | sort | uniq -c
```

### **Success Criteria:**
- ✅ 8 monthly input files created in `NYC-2024-monthly-aggregated/`
- ✅ 8 monthly output files created in `monthly_output_files/`
- ✅ Each output file has 15 columns
- ✅ `Destination_Coordinate_Source` column present
- ✅ Coordinate source distribution shows hierarchy working
- ✅ No error messages in log files

---

## 🎉 **Pipeline Completion**

Upon successful completion, you will have:
- **Reduced file count**: From 22 weekly files to 8 monthly files
- **Enhanced data quality**: Updated coordinate hierarchy and multi-polygon handling
- **Complete traceability**: Both source and destination coordinate source tracking
- **Monthly temporal resolution**: Better statistical significance for analysis
- **Ready for analysis**: Clean, aggregated monthly tract flow data

**Next steps:**
1. Analyze monthly tract flow patterns
2. Compare with previous weekly analysis results
3. Use monthly data for downstream spatial analysis
4. Archive or remove weekly output files if no longer needed
