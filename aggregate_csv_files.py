import os
import pandas as pd
import glob
from math import radians, sin, cos, sqrt, atan2

def haversine_distance(lat1, lon1, lat2, lon2):
    """
    Calculate the great circle distance between two points
    on the earth (specified in decimal degrees)

    Returns:
        float: Distance in kilometers
    """
    # Convert latitude and longitude from degrees to radians
    lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])

    # Haversine formula
    dlon = lon2 - lon1
    dlat = lat2 - lat1
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * atan2(sqrt(a), sqrt(1-a))
    r = 6371  # Radius of Earth in kilometers

    return r * c  # Distance in kilometers

def perform_proper_aggregation(combined_df):
    """
    Performs proper aggregation of Source-Destination pairs across multiple time periods.

    This function:
    1. Groups by Source-Destination pairs
    2. Sums AggFlow values for identical pairs
    3. Preserves coordinate and population data (taking first non-null values)
    4. Recalculates <PERSON><PERSON><PERSON> and Population_within_Circle based on aggregated flows
    5. Maintains Source_Coordinate_Source tracking

    Args:
        combined_df (pd.DataFrame): Combined dataframe from all input files

    Returns:
        pd.DataFrame: Properly aggregated dataframe with unique Source-Destination pairs
    """
    print("  Grouping by Source-Destination pairs...")

    # Define aggregation functions for each column
    agg_functions = {
        'AggFlow': 'sum',  # Sum flows across time periods
        'Source_Latitude': 'first',  # Take first non-null value
        'Source_Longitude': 'first',
        'Destination_Latitude': 'first',
        'Destination_Longitude': 'first',
        'Source_Population': 'first',
        'Destination_Population': 'first',
        'Source_Coordinate_Source': 'first'  # Preserve coordinate source tracking
    }

    # Group by Source-Destination pairs and aggregate
    aggregated_df = combined_df.groupby(['Source', 'Destination']).agg(agg_functions).reset_index()

    print(f"  Reduced from {len(combined_df)} rows to {len(aggregated_df)} unique Source-Destination pairs")

    # Recalculate Source_Destination_Distance for aggregated data
    print("  Recalculating distances for aggregated pairs...")
    aggregated_df['Source_Destination_Distance'] = aggregated_df.apply(
        lambda row: haversine_distance(
            row['Source_Latitude'],
            row['Source_Longitude'],
            row['Destination_Latitude'],
            row['Destination_Longitude']
        ) if (pd.notna(row['Source_Latitude']) and pd.notna(row['Source_Longitude']) and
              pd.notna(row['Destination_Latitude']) and pd.notna(row['Destination_Longitude'])) else 0.0,
        axis=1
    )

    # Load coordinate and population data for buffer and population calculations
    print("  Loading coordinate and population data for recalculations...")
    tract_coords, tract_populations = load_coordinate_and_population_data()

    # Recalculate Buffer and Population_within_Circle for aggregated data
    print("  Recalculating Buffer and Population_within_Circle for aggregated flows...")
    aggregated_df = recalculate_buffer_and_population(aggregated_df, tract_coords, tract_populations)

    return aggregated_df

def load_coordinate_and_population_data():
    """
    Loads coordinate and population data from the same sources used in process_tract_flows.py
    following the 3-tier hierarchy: census → tessellation → gazetteer

    Returns:
        tuple: (tract_coords dict, tract_populations dict)
    """
    tract_coords = {}
    tract_populations = {}

    # Load coordinates from gazetteer data (3rd priority)
    gaz_file = '2020_Gaz_tracts_national.txt'
    if os.path.exists(gaz_file):
        print(f"    Loading coordinates from {gaz_file}...")
        gaz_df = pd.read_csv(gaz_file, sep='\t', dtype={'GEOID': str})
        for _, row in gaz_df.iterrows():
            tract_id = str(row['GEOID'])
            tract_coords[tract_id] = {
                'latitude': row['INTPTLAT'],
                'longitude': row['INTPTLONG']
            }

    # Load coordinates and population from census data (1st priority)
    census_file = 'population files/census-populations-2020-tract-new-york.csv'
    if os.path.exists(census_file):
        print(f"    Loading coordinates and population from {census_file}...")
        census_df = pd.read_csv(census_file, dtype={'tract': str})
        for _, row in census_df.iterrows():
            tract_id = str(row['tract'])
            # Coordinates (1st priority - will override gazetteer)
            tract_coords[tract_id] = {
                'latitude': row['latitude'],
                'longitude': row['longitude']
            }
            # Population data
            tract_populations[tract_id] = row['population']

    # Load coordinates and population from tessellation data (2nd priority)
    tessellation_file = 'population files/tessellation_with_population.geojson'
    if os.path.exists(tessellation_file):
        print(f"    Loading coordinates and population from {tessellation_file}...")
        import json
        with open(tessellation_file, 'r') as f:
            tessellation_data = json.load(f)

        for feature in tessellation_data['features']:
            tract_id = str(feature['properties']['tract'])

            # Calculate centroid for coordinates (2nd priority - will override gazetteer but not census)
            # Only add if not already loaded from census data
            if tract_id not in tract_coords:
                coordinates = feature['geometry']['coordinates'][0]
                if coordinates:
                    lats = [coord[1] for coord in coordinates]
                    lons = [coord[0] for coord in coordinates]
                    centroid_lat = sum(lats) / len(lats)
                    centroid_lon = sum(lons) / len(lons)

                    tract_coords[tract_id] = {
                        'latitude': centroid_lat,
                        'longitude': centroid_lon
                    }

            # Population data (if not already from census)
            if tract_id not in tract_populations and 'population' in feature['properties']:
                tract_populations[tract_id] = feature['properties']['population']

    print(f"    Loaded coordinates for {len(tract_coords)} tracts")
    print(f"    Loaded population data for {len(tract_populations)} tracts")

    return tract_coords, tract_populations

def recalculate_buffer_and_population(aggregated_df, tract_coords, tract_populations):
    """
    Recalculates Buffer and Population_within_Circle for aggregated Source-Destination pairs.

    Args:
        aggregated_df (pd.DataFrame): Aggregated dataframe with unique Source-Destination pairs
        tract_coords (dict): Dictionary of tract coordinates
        tract_populations (dict): Dictionary of tract populations

    Returns:
        pd.DataFrame: Dataframe with recalculated Buffer and Population_within_Circle values
    """
    # Initialize new columns
    aggregated_df['Buffer'] = 0
    aggregated_df['Population_within_Circle'] = 0.0
    aggregated_df['Missing_Population_Tracts'] = 0

    total_rows = len(aggregated_df)

    for idx, row in aggregated_df.iterrows():
        if idx % 1000 == 0:
            print(f"    Processing row {idx}/{total_rows}...")

        source_tract = str(row['Source'])
        dest_tract = str(row['Destination'])
        distance = row['Source_Destination_Distance']

        # Skip if distance is invalid
        if pd.isna(distance) or distance <= 0:
            continue

        # Get destination coordinates
        dest_lat = row['Destination_Latitude']
        dest_lon = row['Destination_Longitude']

        # Skip if coordinates are invalid
        if pd.isna(dest_lat) or pd.isna(dest_lon):
            continue

        buffer_count = 0
        population_within_circle = 0.0
        missing_population_tracts = 0

        # Check each tract to see if it's within the circle
        for tract_id, coords in tract_coords.items():
            # Skip source and destination tracts
            if tract_id == source_tract or tract_id == dest_tract:
                continue

            tract_lat = coords['latitude']
            tract_lon = coords['longitude']

            # Skip if tract coordinates are invalid
            if pd.isna(tract_lat) or pd.isna(tract_lon):
                continue

            # Calculate distance from destination to this tract
            tract_distance = haversine_distance(dest_lat, dest_lon, tract_lat, tract_lon)

            # If distance is less than or equal to the radius (source-destination distance),
            # the tract is within the circle
            if tract_distance <= distance:
                buffer_count += 1

                # Get tract population
                if tract_id in tract_populations:
                    tract_population = tract_populations[tract_id]
                    population_within_circle += tract_population
                else:
                    missing_population_tracts += 1

        # Save the calculated values
        aggregated_df.at[idx, 'Buffer'] = buffer_count
        aggregated_df.at[idx, 'Population_within_Circle'] = population_within_circle
        aggregated_df.at[idx, 'Missing_Population_Tracts'] = missing_population_tracts

    return aggregated_df

def aggregate_csv_files():
    """
    Properly aggregates all CSV files from the output_files directory into a single CSV file.

    This function:
    1. Combines all Source-Destination pairs across all input files into unique pairs
    2. Sums the AggFlow values for identical Source-Destination pairs from different time periods
    3. Recalculates Buffer values based on the final aggregated flows
    4. Recalculates Population_within_Circle based on the properly aggregated data
    5. Maintains coordinate and population data from the established 3-tier hierarchy
    6. Preserves the Source_Coordinate_Source tracking for traceability

    Saves the result as 'aggregated_tract_flows.csv' in the root directory.
    """
    print("Starting proper CSV aggregation process...")

    # Path to the output_files directory
    output_dir = 'output_files'

    # Get a list of all CSV files in the output_files directory
    csv_files = glob.glob(os.path.join(output_dir, '*.csv'))

    if not csv_files:
        print("No CSV files found in the output_files directory.")
        return

    print(f"Found {len(csv_files)} CSV files to aggregate.")

    # Create an empty list to store dataframes
    dfs = []

    # Read each CSV file and append to the list
    for i, file in enumerate(csv_files):
        print(f"Processing file {i+1}/{len(csv_files)}: {os.path.basename(file)}")
        try:
            # Read the CSV file
            df = pd.read_csv(file)

            # Remove Source_File column if it exists from previous runs
            if 'Source_File' in df.columns:
                df = df.drop(columns=['Source_File'])

            # Append to the list
            dfs.append(df)

            print(f"  - Added {len(df)} rows from {os.path.basename(file)}")
        except Exception as e:
            print(f"  - Error processing {file}: {str(e)}")

    if not dfs:
        print("No data was successfully read from the CSV files.")
        return

    # Combine all dataframes
    print("Combining all dataframes...")
    combined_df = pd.concat(dfs, ignore_index=True)
    print(f"Total rows before aggregation: {len(combined_df)}")

    # Perform proper aggregation
    print("Performing proper aggregation by Source-Destination pairs...")
    aggregated_df = perform_proper_aggregation(combined_df)

    # Save the aggregated dataframe to a new CSV file
    output_file = 'aggregated_tract_flows.csv'
    print(f"Saving properly aggregated data to {output_file}...")
    aggregated_df.to_csv(output_file, index=False)

    print(f"Proper aggregation complete. Total unique Source-Destination pairs: {len(aggregated_df)}")
    print(f"Output saved to: {output_file}")

if __name__ == "__main__":
    aggregate_csv_files()
