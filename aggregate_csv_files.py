import os
import pandas as pd
import glob

def aggregate_csv_files():
    """
    Aggregates all CSV files from the output_files directory into a single CSV file.
    Saves the result as 'aggregated_tract_flows.csv' in the root directory.
    """
    print("Starting CSV aggregation process...")
    
    # Path to the output_files directory
    output_dir = 'output_files'
    
    # Get a list of all CSV files in the output_files directory
    csv_files = glob.glob(os.path.join(output_dir, '*.csv'))
    
    if not csv_files:
        print("No CSV files found in the output_files directory.")
        return
    
    print(f"Found {len(csv_files)} CSV files to aggregate.")
    
    # Create an empty list to store dataframes
    dfs = []
    
    # Read each CSV file and append to the list
    for i, file in enumerate(csv_files):
        print(f"Processing file {i+1}/{len(csv_files)}: {os.path.basename(file)}")
        try:
            # Read the CSV file
            df = pd.read_csv(file)
            
            # Add a column to identify the source file (optional)
            file_name = os.path.basename(file)
            df['Source_File'] = file_name
            
            # Append to the list
            dfs.append(df)
            
            print(f"  - Added {len(df)} rows from {file_name}")
        except Exception as e:
            print(f"  - Error processing {file}: {str(e)}")
    
    if not dfs:
        print("No data was successfully read from the CSV files.")
        return
    
    # Combine all dataframes
    print("Combining all dataframes...")
    combined_df = pd.concat(dfs, ignore_index=True)
    
    # Save the combined dataframe to a new CSV file
    output_file = 'aggregated_tract_flows.csv'
    print(f"Saving combined data to {output_file}...")
    combined_df.to_csv(output_file, index=False)
    
    print(f"Aggregation complete. Total rows: {len(combined_df)}")
    print(f"Output saved to: {output_file}")

if __name__ == "__main__":
    aggregate_csv_files()
