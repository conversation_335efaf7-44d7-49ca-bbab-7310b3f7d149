# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Large directories that don't contain useful data
data/
NYC-2024-weekly-20250509T045249Z-1-002/

# These files are now tracked with Git LFS
# population files/
# *.csv.gz
# NYC_Weekly_Patterns_Foot_Traffic_Full_Historical_Data_*.csv
# tessellation_with_population.geojson
# tract_flows_with_population.csv
# tract_flows.csv

# Distribution / packaging
dist/
build/
*.egg-info/

# Virtual environments
venv/
env/
.env/
.venv/

# IDE specific files
.idea/
.vscode/
*.swp
*.swo

# OS specific files
.DS_Store
Thumbs.db

# Excel files (optional, comment out if you want to include them)
# *.xlsx
# *.xls

# Git LFS is now configured in .gitattributes
