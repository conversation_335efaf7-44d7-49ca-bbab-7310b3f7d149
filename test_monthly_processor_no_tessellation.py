#!/usr/bin/env python3
"""
Test Monthly Processor Without Tessellation

This script tests the monthly processor without tessellation loading to isolate
the population lookup issue.

Author: Augment Agent
Date: 2024
"""

import pandas as pd
import os
import glob
import gzip

def load_coordinate_and_population_data_no_tessellation():
    """Load coordinate and population data without tessellation (for testing)"""
    print("Loading coordinate and population data (no tessellation)...")
    
    tract_coords = {}
    tract_populations = {}
    coord_sources = {}
    
    # Priority 1: Load from census data
    print("  Priority 1: Loading census data...")
    census_file = 'population files/census-populations-2020-tract-new-york.csv'
    if os.path.exists(census_file):
        census_df = pd.read_csv(census_file)
        census_count = 0
        for _, row in census_df.iterrows():
            # Fix tract ID format - ensure it's a clean string without .0 suffix
            tract_id = str(int(float(row['tract'])))
            if pd.notna(row['latitude']) and pd.notna(row['longitude']):
                tract_coords[tract_id] = {
                    'latitude': row['latitude'],
                    'longitude': row['longitude']
                }
                coord_sources[tract_id] = 'census'
                census_count += 1
            if pd.notna(row['population']):
                tract_populations[tract_id] = row['population']
        print(f"    Loaded {census_count} coordinates from census data")
    
    # Priority 2: Load from gazetteer data
    print("  Priority 2: Loading gazetteer data...")
    gaz_file = '2020_Gaz_tracts_national.txt'
    if os.path.exists(gaz_file):
        gazetteer_count = 0
        with open(gaz_file, 'r') as f:
            next(f)  # Skip header
            for line in f:
                parts = line.strip().split('\t')
                if len(parts) >= 8:
                    tract_id = parts[1]
                    if tract_id not in tract_coords:
                        try:
                            lat = float(parts[6])
                            lon = float(parts[7])
                            tract_coords[tract_id] = {
                                'latitude': lat,
                                'longitude': lon
                            }
                            coord_sources[tract_id] = 'gazetteer'
                            gazetteer_count += 1
                        except (ValueError, IndexError):
                            continue
        print(f"    Added {gazetteer_count} coordinates from gazetteer data")
    
    # Skip tessellation for testing
    print("  Priority 3: Skipping tessellation data for testing...")
    
    print(f"  Total coordinates loaded: {len(tract_coords)}")
    print(f"  Total population data loaded: {len(tract_populations)}")
    
    return tract_coords, tract_populations, coord_sources

def test_monthly_processing():
    """Test monthly processing with population lookup"""
    print("🧪 TESTING MONTHLY PROCESSING (NO TESSELLATION)")
    print("=" * 60)
    
    # Load coordinate and population data
    tract_coords, tract_populations, coord_sources = load_coordinate_and_population_data_no_tessellation()
    
    # Create separate population dictionaries
    census_population = {}
    tessellation_population = {}
    for tract_id, population in tract_populations.items():
        source = coord_sources.get(tract_id, 'unknown')
        if source == 'census':
            census_population[tract_id] = population
        elif source == 'tessellation':
            tessellation_population[tract_id] = population
    
    # Debug: Print population dictionary statistics
    print(f"\nPopulation dictionary statistics:")
    print(f"  Total tract_populations: {len(tract_populations)}")
    print(f"  Census population dict: {len(census_population)} entries")
    print(f"  Tessellation population dict: {len(tessellation_population)} entries")
    
    # Show sample tract IDs from each population source
    if census_population:
        sample_census = list(census_population.keys())[:5]
        print(f"  Sample census tract IDs: {sample_census}")
    
    # Test with first monthly file
    input_files = glob.glob('NYC-2024-monthly-aggregated/*.csv.gz')
    if not input_files:
        print("❌ No monthly input files found!")
        return False
    
    input_file = input_files[0]
    print(f"\n📊 Testing with: {os.path.basename(input_file)}")
    
    try:
        # Read small sample
        with gzip.open(input_file, 'rt', encoding='utf-8', errors='replace') as f:
            df = pd.read_csv(f, nrows=10)
        
        # Extract tract IDs
        dest_tracts = df['poi_cbg'].astype(str).str[:11].unique()
        print(f"  Sample destination tracts: {dest_tracts.tolist()}")
        
        # Test population lookup
        def get_tract_population(tract_id):
            if tract_id in census_population:
                return census_population[tract_id]
            elif tract_id in tessellation_population:
                return tessellation_population[tract_id]
            return None
        
        print(f"\n🔍 Testing population lookup:")
        found = 0
        for tract_id in dest_tracts:
            population = get_tract_population(tract_id)
            if population is not None:
                print(f"   ✅ {tract_id}: {population}")
                found += 1
            else:
                print(f"   ❌ {tract_id}: NOT FOUND")
        
        print(f"\n📊 Population lookup results: {found}/{len(dest_tracts)} found")
        
        return found > 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_monthly_processing()
    if success:
        print(f"\n✅ POPULATION LOOKUP IS WORKING!")
        print(f"   The issue may be in tessellation processing or the main loop.")
    else:
        print(f"\n❌ POPULATION LOOKUP FAILED!")
        print(f"   Need to investigate further.")
