# Tract Flow Analysis

## Overview
This project processes geospatial mobility data to analyze flows between census tracts. It takes raw visit data at the Census Block Group (CBG) level and aggregates it to the tract level, calculating flows between tracts, identifying points of interest (POIs) within buffer zones, and computing distances between source and destination tracts. The project also includes population analysis to understand the relationship between census tract populations and mobility patterns.

## Features
- Converts Census Block Group (CBG) level data to tract level (using first 11 digits of CBG codes)
- Calculates aggregated flows between source and destination tracts
- Computes buffer counts (number of POIs within a circle centered at the destination and touching the source)
- Calculates the Haversine distance between source and destination tracts in kilometers
- Handles CSV files with special JSON formatting for visitor home CBGs
- Generates a comprehensive tract-level output file with all required metrics
- Includes source and destination coordinates (latitude and longitude) in the output
- Uses multiple data sources to find tract coordinates:
  - Original data coordinates
  - Census population data (census-populations-2020-tract-new-york.csv)
  - Tessellation data with population (tessellation_with_population.geojson)
  - TIGER/Line Gazetteer files (2020_Gaz_tracts_national.txt)
- Includes population data for source and destination tracts
- Calculates total population within the circle centered at the destination tract
- Tracks missing population data to provide context for population calculations

## Data Format
The input data can be either an Excel file (.xlsx) or a CSV file containing the following columns:
- `placekey`: Unique identifier for a place
- `poi_cbg`: Census Block Group where the POI is located
- `visitor_home_cbgs`: JSON string containing visitor home CBGs and visit counts
- `raw_visit_counts`: Total number of visits to the POI
- `latitude`: Latitude coordinate of the POI
- `longitude`: Longitude coordinate of the POI
- `polygon_wkt` (optional): Well-Known Text representation of the POI's polygon geometry

The `visitor_home_cbgs` column can be in different JSON formats, including standard JSON or with extra quotes (as seen in some SafeGraph datasets).

## Output
The script generates a single CSV file:

### Tract-level flows: `tract_flows_with_population.csv`
- `Source`: Source tract number (11-digit code)
- `Destination`: Destination tract number (11-digit code)
- `AggFlow`: Total aggregated visits from the Source to the Destination
- `Buffer`: Number of places located within the circle centered at the Destination and touching the Source, excluding POIs located within the Destination or Source themselves
- `Source_Destination_Distance`: Distance in kilometers between the Source and Destination tracts, calculated using the Haversine formula
- `Source_Latitude`: Latitude coordinate of the Source tract
- `Source_Longitude`: Longitude coordinate of the Source tract
- `Destination_Latitude`: Latitude coordinate of the Destination tract
- `Destination_Longitude`: Longitude coordinate of the Destination tract
- `Source_Population`: Population of the Source tract
- `Destination_Population`: Population of the Destination tract
- `Population_within_Circle`: Total population of all tracts within the circle centered at the Destination and with a radius equal to the distance between the Source and Destination, excluding the population of the Source and Destination tracts themselves
- `Missing_Population_Tracts`: Number of tracts within the circle that are missing population data, providing context for the reliability of the Population_within_Circle value

The buffer values can range from 0 to over 150,000 depending on the density and distribution of POIs in the dataset. In datasets with high POI density (like NYC), a significant percentage of tract pairs will have non-zero buffer values.

## Requirements
- Python 3.6+
- pandas
- re (for regex pattern matching)
- math (for Haversine distance calculation)
- json (for parsing GeoJSON files)

## Usage

### Processing Tract Flows

1. Ensure your input files are in the `NYC-2024-weekly-20250509T045249Z-1-002/NYC-2024-weekly/` directory
2. Run the script:
```
python process_tract_flows.py
```
3. The output files will be saved in the `output_files` directory with filenames like `tract_flows_with_population_YYYY-MM-DD.csv`

The script will also display:
- A sample of the tract-level flows
- The number of tract pairs with non-zero buffer values
- A sample of tract pairs with non-zero buffer values (if any)

## Example Output

### Tract-level flows: `tract_flows_with_population.csv`
```
Source,Destination,AggFlow,Buffer,Source_Destination_Distance,Source_Latitude,Source_Longitude,Destination_Latitude,Destination_Longitude,Source_Population,Destination_Population,Population_within_Circle,Missing_Population_Tracts
01003010400,36061026500,6,159142,1667.33,30.720027,-87.624544,40.848604,-73.937804,,1019.0,1405837,100
01003010703,36061026500,6,0,0.0,0.0,0.0,40.848604,-73.937804,,1019.0,0,0
01003010705,36061001300,9,0,0.0,0.0,0.0,40.711564,-74.011411,,1629.0,0,0
01003010705,36061001501,7,0,0.0,0.0,0.0,40.711086,-74.004837,,4374.0,0,0
01003010705,36061001502,4,0,0.0,0.0,0.0,40.704647,-74.003306,,2091.0,0,0
```

### Sample of tract pairs with non-zero buffer values
```
Source,Destination,AggFlow,Buffer,Source_Destination_Distance,Source_Latitude,Source_Longitude,Destination_Latitude,Destination_Longitude,Source_Population,Destination_Population,Population_within_Circle,Missing_Population_Tracts
01003010400,36061026500,6,159142,1667.33,30.720027,-87.624544,40.848604,-73.937804,,1019.0,1405837,100
01003011201,36081033100,4,159359,1664.89,30.540806,-87.891618,40.771195,-73.865459,,0.0,1406856,100
01015000700,36059414001,4,159374,1664.74,33.707754,-85.791935,40.690701,-73.581226,,1109.0,1405747,100
01045021200,36047001500,4,159374,1664.74,31.242794,-85.737284,40.690576,-73.983066,,,1406856,99
01045021200,36061000900,4,157362,1685.31,31.242794,-85.737284,40.703461,-74.014072,,2016.0,1404840,100
```

### Statistics (NYC Data)
```
Number of tract pairs with non-zero buffer values: 64,305 (90.0% of all pairs)

Buffer statistics:
  Min: 0
  Max: 159,374
  Mean: 71,523.45
  Median: 157,362.0

Source_Destination_Distance statistics:
  Min: 0.00 km
  Max: 1,685.31 km
  Mean: 1,664.74 km
  Median: 1,667.33 km

Coordinate statistics:
  Records with valid source coordinates: 64,305 (90.0% of all pairs)
  Records with missing source coordinates: 7,191 (10.0% of all pairs)
```

## Understanding Tract Numbers
A 12-digit tract number typically refers to a unique identifier used in geographic or census data, particularly in the U.S. Census Bureau's TIGER/Line data. The format of a Census Tract Code is usually structured as:
- SS = 2-digit State FIPS code
- CCC = 3-digit County FIPS code
- TTTTTT = 6-digit Census Tract code

In this project, we use the first 11 digits of the CBG code to represent the tract.

## Buffer Calculation Logic

The buffer represents the number of POIs that are located within a circle centered at the destination tract and with a radius equal to the distance between the source and destination tracts, excluding POIs in the source or destination tracts themselves.

Here's how the buffer is calculated:

1. **Define the Circle**:
   - The center of the circle is at the destination tract's coordinates
   - The radius of the circle is the Haversine distance between the source and destination tracts

2. **Identify POIs to Count**:
   - We iterate through all tracts that have POIs
   - We exclude the source tract and destination tract
   - For each remaining tract, we check if it falls within the circle

3. **Determine if a Tract is Within the Circle**:
   - We calculate the Haversine distance between the destination tract and the current tract
   - If this distance is less than or equal to the radius (source-destination distance), the tract is within the circle

4. **Count POIs**:
   - For each tract within the circle, we count the number of POIs in that tract
   - We add this count to our running total (buffer_count)

The buffer provides a measure of the "attractiveness" or "accessibility" of the area surrounding the destination. A high buffer count indicates that there are many other POIs within the same travel distance as the source-to-destination journey.

## Population within Circle Calculation

The Population_within_Circle represents the total population of all tracts that fall within the circle centered at the destination tract and with a radius equal to the distance between the source and destination tracts, excluding the population of the source and destination tracts themselves.

Here's how the population within circle is calculated:

1. **Define the Circle**:
   - Same as for buffer calculation: center at destination coordinates, radius equal to source-destination distance

2. **Identify Tracts to Include**:
   - We iterate through all tracts that have POIs (same as buffer calculation)
   - We exclude the source tract and destination tract
   - For each remaining tract, we check if it falls within the circle

3. **Determine if a Tract is Within the Circle**:
   - Same as buffer calculation: calculate Haversine distance between destination and current tract
   - If distance is less than or equal to the radius, the tract is within the circle

4. **Sum Population**:
   - For each tract within the circle, we get its population from available data sources
   - If population data is available, we add it to the running total
   - If population data is missing, we increment the missing_population_tracts counter

5. **Track Missing Data**:
   - We keep count of tracts within the circle that are missing population data
   - This provides context for the reliability of the population calculation

The Population_within_Circle provides demographic information about the area surrounding the destination tract, which can be useful for analyzing mobility patterns and understanding the attractiveness of different areas. The Missing_Population_Tracts column helps users understand the completeness of the population calculation.

## Coordinate Sources

The script uses multiple sources to find coordinates for census tracts, in the following order of preference:

1. **Original Data Coordinates**:
   - Coordinates extracted from the input data file
   - These are typically the most accurate for the specific dataset

2. **Census Population Data**:
   - Coordinates from `population files/census-populations-2020-tract-new-york.csv`
   - Contains coordinates for New York census tracts

3. **Tessellation Data**:
   - Coordinates from `population files/tessellation_with_population.geojson`
   - Contains polygon data for census tracts, from which centroids are calculated

4. **TIGER/Line Gazetteer Files**:
   - Coordinates from `2020_Gaz_tracts_national.txt`
   - Contains coordinates for all census tracts in the United States
   - Used as a fallback when other sources don't have the coordinates

If coordinates cannot be found in any of these sources, they are set to 0.0, and the Haversine distance is also set to 0.0 for those records.

## License
[MIT License](LICENSE)
