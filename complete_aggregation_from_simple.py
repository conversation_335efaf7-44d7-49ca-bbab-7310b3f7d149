#!/usr/bin/env python3
"""
Complete the aggregation by adding buffer and population calculations to the simple aggregated data.
This approach is more efficient as it starts with the already-aggregated 716,549 unique pairs.
"""

import os
import pandas as pd
import json
from math import radians, sin, cos, sqrt, atan2

def haversine_distance(lat1, lon1, lat2, lon2):
    """
    Calculate the great circle distance between two points 
    on the earth (specified in decimal degrees)
    """
    # Convert decimal degrees to radians
    lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
    
    # Haversine formula
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * atan2(sqrt(a), sqrt(1-a))
    r = 6371  # Radius of earth in kilometers
    return r * c

def load_coordinate_and_population_data():
    """
    Load coordinate and population data from all available sources using 3-tier hierarchy.
    Returns tract_coords and tract_populations dictionaries.
    """
    tract_coords = {}
    tract_populations = {}
    
    print("Loading coordinate and population data...")
    
    # 1. Load from census data (highest priority)
    census_file = 'population files/census-populations-2020-tract-new-york.csv'
    if os.path.exists(census_file):
        print(f"  Loading from {census_file}...")
        census_df = pd.read_csv(census_file, dtype={'tract': str})
        for _, row in census_df.iterrows():
            tract_id = str(row['tract'])
            if pd.notna(row['latitude']) and pd.notna(row['longitude']):
                tract_coords[tract_id] = {
                    'latitude': row['latitude'],
                    'longitude': row['longitude']
                }
            if pd.notna(row['population']):
                tract_populations[tract_id] = row['population']
        print(f"    Loaded {len([k for k in tract_coords.keys()])} coordinates from census data")
    
    # 2. Load from tessellation data (medium priority - only if not in census)
    tessellation_file = 'population files/tessellation_with_population.geojson'
    if os.path.exists(tessellation_file):
        print(f"  Loading from {tessellation_file}...")
        with open(tessellation_file, 'r') as f:
            tessellation_data = json.load(f)
        
        tessellation_count = 0
        for feature in tessellation_data['features']:
            # Extract tract ID from tile_ID (first 11 digits)
            tile_id = feature['properties'].get('tile_ID', '')
            if tile_id:
                tract_id = str(tile_id)[:11]
                
                # Calculate centroid for coordinates (only if not already from census)
                if tract_id not in tract_coords:
                    geometry = feature.get('geometry', {})
                    if geometry.get('type') == 'Polygon':
                        coords = geometry.get('coordinates', [])[0]
                        if coords:
                            sum_x = sum(p[0] for p in coords)
                            sum_y = sum(p[1] for p in coords)
                            count = len(coords)
                            centroid_lon = sum_x / count
                            centroid_lat = sum_y / count
                            
                            tract_coords[tract_id] = {
                                'latitude': centroid_lat,
                                'longitude': centroid_lon
                            }
                            tessellation_count += 1
                
                # Population data (if not already from census)
                if tract_id not in tract_populations and 'population' in feature['properties']:
                    tract_populations[tract_id] = feature['properties']['population']
        
        print(f"    Added {tessellation_count} coordinates from tessellation data")
    
    # 3. Load from gazetteer data (lowest priority - only if not in census or tessellation)
    gaz_file = '2020_Gaz_tracts_national.txt'
    if os.path.exists(gaz_file):
        print(f"  Loading from {gaz_file}...")
        gaz_df = pd.read_csv(gaz_file, sep='\t', dtype={'GEOID': str})
        gaz_df.columns = gaz_df.columns.str.strip()
        
        gazetteer_count = 0
        for _, row in gaz_df.iterrows():
            tract_id = str(row['GEOID'])
            if tract_id not in tract_coords:
                if pd.notna(row['INTPTLAT']) and pd.notna(row['INTPTLONG']):
                    tract_coords[tract_id] = {
                        'latitude': row['INTPTLAT'],
                        'longitude': row['INTPTLONG']
                    }
                    gazetteer_count += 1
        
        print(f"    Added {gazetteer_count} coordinates from gazetteer data")
    
    print(f"  Total coordinates loaded: {len(tract_coords)}")
    print(f"  Total population data loaded: {len(tract_populations)}")
    
    return tract_coords, tract_populations

def add_buffer_and_population_calculations(df, tract_coords, tract_populations):
    """
    Add buffer and population calculations to the aggregated dataframe.
    """
    print("Adding buffer and population calculations...")
    print(f"Processing {len(df)} unique Source-Destination pairs...")
    
    # Initialize new columns
    df['Buffer'] = 0
    df['Population_within_Circle'] = 0.0
    df['Missing_Population_Tracts'] = 0
    
    # Convert tract coordinates to lists for faster processing
    tract_ids = list(tract_coords.keys())
    tract_lats = [tract_coords[tid]['latitude'] for tid in tract_ids]
    tract_lons = [tract_coords[tid]['longitude'] for tid in tract_ids]
    
    total_rows = len(df)
    
    for idx, row in df.iterrows():
        if idx % 5000 == 0:
            print(f"  Progress: {idx:,}/{total_rows:,} ({idx/total_rows*100:.1f}%)...")
        
        dest_lat = row['Destination_Latitude']
        dest_lon = row['Destination_Longitude']
        distance = row['Source_Destination_Distance']
        
        # Skip if coordinates or distance are invalid
        if pd.isna(dest_lat) or pd.isna(dest_lon) or pd.isna(distance) or distance <= 0:
            continue
        
        buffer_count = 0
        population_within_circle = 0.0
        missing_population_tracts = 0
        
        # Check each tract to see if it's within the circle
        for i, tract_id in enumerate(tract_ids):
            tract_lat = tract_lats[i]
            tract_lon = tract_lons[i]
            
            # Skip if tract coordinates are invalid
            if pd.isna(tract_lat) or pd.isna(tract_lon):
                continue
            
            # Calculate distance from destination to this tract
            tract_distance = haversine_distance(dest_lat, dest_lon, tract_lat, tract_lon)
            
            # If distance is less than or equal to the radius (source-destination distance),
            # the tract is within the circle
            if tract_distance <= distance:
                buffer_count += 1
                
                # Get tract population
                if tract_id in tract_populations:
                    tract_population = tract_populations[tract_id]
                    if tract_population is not None:
                        population_within_circle += tract_population
                    else:
                        missing_population_tracts += 1
                else:
                    missing_population_tracts += 1
        
        # Save the calculated values
        df.at[idx, 'Buffer'] = buffer_count
        df.at[idx, 'Population_within_Circle'] = population_within_circle
        df.at[idx, 'Missing_Population_Tracts'] = missing_population_tracts
    
    print(f"  Completed buffer and population calculations for {total_rows:,} pairs")
    return df

def complete_aggregation():
    """
    Complete the aggregation by adding buffer and population calculations to the simple aggregated data.
    """
    print("Starting complete aggregation process...")
    
    # Load the simple aggregated data
    simple_file = 'simple_aggregated_tract_flows.csv'
    if not os.path.exists(simple_file):
        print(f"Error: {simple_file} not found. Please run test_simple_aggregation.py first.")
        return
    
    print(f"Loading simple aggregated data from {simple_file}...")
    df = pd.read_csv(simple_file)
    print(f"Loaded {len(df)} unique Source-Destination pairs")
    
    # Load coordinate and population data
    tract_coords, tract_populations = load_coordinate_and_population_data()
    
    # Add buffer and population calculations
    df = add_buffer_and_population_calculations(df, tract_coords, tract_populations)
    
    # Save the complete aggregated dataframe
    output_file = 'aggregated_tract_flows.csv'
    print(f"Saving complete aggregated data to {output_file}...")
    df.to_csv(output_file, index=False)
    
    print(f"Complete aggregation finished successfully!")
    print(f"Output saved to: {output_file}")
    print(f"Total unique Source-Destination pairs: {len(df):,}")

if __name__ == "__main__":
    complete_aggregation()
