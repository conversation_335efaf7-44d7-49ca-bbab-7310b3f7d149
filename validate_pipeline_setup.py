#!/usr/bin/env python3
"""
Monthly Pipeline Setup Validator

This script validates that all prerequisites are met before running the
Monthly Aggregated Tract Flow Analysis Pipeline.

Usage: python validate_pipeline_setup.py

Author: Augment Agent
Date: 2024
"""

import os
import glob
import sys

def check_weekly_input_files():
    """Check if all weekly input files are present"""
    print("📁 Checking weekly input files...")
    
    weekly_dir = 'NYC-2024-weekly-20250509T045249Z-1-002/NYC-2024-weekly'
    if not os.path.exists(weekly_dir):
        print(f"   ❌ Weekly directory not found: {weekly_dir}")
        return False
    
    weekly_files = glob.glob(f'{weekly_dir}/*DATE_RANGE_START*.csv.gz')
    print(f"   📊 Found {len(weekly_files)} weekly files")
    
    if len(weekly_files) != 22:
        print(f"   ⚠️  Expected 22 weekly files, found {len(weekly_files)}")
        return False
    
    # Check file sizes
    total_size = 0
    for file_path in weekly_files:
        size = os.path.getsize(file_path)
        total_size += size
    
    print(f"   📈 Total size: {total_size / (1024**3):.1f} GB")
    print(f"   ✅ Weekly input files validated")
    return True

def check_coordinate_files():
    """Check if coordinate and population files are present"""
    print("\n🗺️  Checking coordinate and population files...")
    
    required_files = {
        'Gazetteer coordinates': '2020_Gaz_tracts_national.txt',
        'Census population': 'population files/census-populations-2020-tract-new-york.csv',
        'Tessellation data': 'population files/tessellation_with_population.geojson'
    }
    
    all_present = True
    for name, file_path in required_files.items():
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            if name == 'Tessellation data':
                size_gb = size / (1024**3)
                print(f"   ✅ {name}: {file_path} ({size_gb:.1f} GB)")
            else:
                size_mb = size / (1024**2)
                print(f"   ✅ {name}: {file_path} ({size_mb:.1f} MB)")
        else:
            print(f"   ❌ {name}: {file_path} (NOT FOUND)")
            all_present = False
    
    return all_present

def check_pipeline_scripts():
    """Check if pipeline scripts are present"""
    print("\n🔧 Checking pipeline scripts...")
    
    required_scripts = {
        'Monthly aggregation': 'create_monthly_aggregated_optimized.py',
        'Monthly tract flow processor': 'process_monthly_tract_flows.py',
        'Original tract flow processor': 'process_tract_flows.py'
    }
    
    all_present = True
    for name, script_path in required_scripts.items():
        if os.path.exists(script_path):
            print(f"   ✅ {name}: {script_path}")
        else:
            print(f"   ❌ {name}: {script_path} (NOT FOUND)")
            all_present = False
    
    return all_present

def check_python_dependencies():
    """Check if required Python packages are available"""
    print("\n🐍 Checking Python dependencies...")
    
    required_packages = ['pandas', 'numpy', 'json', 'gzip', 'glob', 're', 'os', 'datetime']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} (NOT AVAILABLE)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n   ⚠️  Missing packages: {', '.join(missing_packages)}")
        print(f"   💡 Install with: pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_disk_space():
    """Check available disk space"""
    print("\n💾 Checking disk space...")
    
    try:
        import shutil
        total, used, free = shutil.disk_usage('.')
        free_gb = free / (1024**3)
        total_gb = total / (1024**3)
        
        print(f"   📊 Total space: {total_gb:.1f} GB")
        print(f"   📊 Free space: {free_gb:.1f} GB")
        
        if free_gb < 10:
            print(f"   ⚠️  Low disk space! Recommend at least 10 GB free")
            print(f"   💡 Consider freeing up space before running pipeline")
            return False
        else:
            print(f"   ✅ Sufficient disk space available")
            return True
            
    except Exception as e:
        print(f"   ⚠️  Could not check disk space: {e}")
        return True  # Don't fail validation for this

def check_existing_outputs():
    """Check if output directories already exist"""
    print("\n📂 Checking for existing output directories...")
    
    output_dirs = ['NYC-2024-monthly-aggregated', 'monthly_output_files']
    existing_dirs = []
    
    for dir_name in output_dirs:
        if os.path.exists(dir_name):
            files = glob.glob(f'{dir_name}/*')
            print(f"   ⚠️  {dir_name} exists with {len(files)} files")
            existing_dirs.append(dir_name)
        else:
            print(f"   ✅ {dir_name} does not exist (will be created)")
    
    if existing_dirs:
        print(f"\n   💡 Existing output directories found. Pipeline will:")
        print(f"      - Overwrite existing files with same names")
        print(f"      - Add new files to existing directories")
        print(f"      - Consider backing up existing data if needed")
    
    return True

def main():
    """Main validation function"""
    print("🔍 MONTHLY PIPELINE SETUP VALIDATION")
    print("=" * 50)
    
    checks = [
        ("Weekly input files", check_weekly_input_files),
        ("Coordinate files", check_coordinate_files),
        ("Pipeline scripts", check_pipeline_scripts),
        ("Python dependencies", check_python_dependencies),
        ("Disk space", check_disk_space),
        ("Existing outputs", check_existing_outputs)
    ]
    
    all_passed = True
    results = []
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
            if not result:
                all_passed = False
        except Exception as e:
            print(f"   ❌ Error during {check_name} check: {e}")
            results.append((check_name, False))
            all_passed = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 VALIDATION SUMMARY")
    print("=" * 50)
    
    for check_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status}: {check_name}")
    
    if all_passed:
        print(f"\n🎉 ALL CHECKS PASSED!")
        print(f"   Ready to run the Monthly Aggregated Tract Flow Analysis Pipeline")
        print(f"\n📝 Next steps:")
        print(f"   1. Run: python create_monthly_aggregated_optimized.py")
        print(f"   2. Run: python process_monthly_tract_flows.py")
        print(f"   3. Follow the MONTHLY_PIPELINE_EXECUTION_GUIDE.md for detailed instructions")
        return True
    else:
        print(f"\n❌ VALIDATION FAILED!")
        print(f"   Please resolve the issues above before running the pipeline")
        print(f"   See MONTHLY_PIPELINE_EXECUTION_GUIDE.md for troubleshooting help")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
