import pandas as pd
import numpy as np
import os
from math import radians, sin, cos, sqrt, atan2
from scipy.spatial import cKDTree
import time

def haversine_distance(lat1, lon1, lat2, lon2):
    """
    Calculate the great circle distance between two points 
    on the earth (specified in decimal degrees)
    
    Returns:
        float: Distance in kilometers
    """
    # Convert latitude and longitude from degrees to radians
    lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
    
    # Haversine formula
    dlon = lon2 - lon1
    dlat = lat2 - lat1
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * atan2(sqrt(a), sqrt(1-a))
    r = 6371  # Radius of Earth in kilometers
    
    return r * c  # Distance in kilometers

def calculate_population_within_circle_optimized():
    """
    Calculates the 'Population within Circle' column for the cleaned dataset using optimized methods.
    
    The Population_within_Circle represents the total population of all tracts 
    that fall within the circle centered at the destination tract and with a radius 
    equal to the distance between the source and destination tracts, excluding the 
    population of the source and destination tracts themselves.
    
    This optimized version uses:
    1. Vectorized operations where possible
    2. KD-Tree for efficient spatial queries
    3. Batch processing to reduce memory pressure
    """
    start_time = time.time()
    print("Starting Population within Circle calculation (optimized version)...")
    
    # Input and output file paths
    input_file = 'NYC_cleaned-2.csv'
    output_dir = 'population_analysis'
    output_file = os.path.join(output_dir, 'NYC_with_population_circle.csv')
    
    # Read the cleaned dataset
    print(f"Reading cleaned dataset from {input_file}...")
    df = pd.read_csv(input_file)
    
    # Standardize column names (convert to lowercase for consistency)
    df.columns = [col.lower().replace('-', '_') for col in df.columns]
    
    # Calculate the distance between source and destination for each row
    print("Calculating distances between source and destination tracts...")
    df['source_destination_distance'] = df.apply(
        lambda row: haversine_distance(
            row['source_latitude'], 
            row['source_longitude'],
            row['destination_latitude'], 
            row['destination_longitude']
        ), 
        axis=1
    )
    
    # Create a unique list of all tracts
    print("Creating unique tract list...")
    all_tracts = set()
    for _, row in df.iterrows():
        all_tracts.add(str(row['source']))
        all_tracts.add(str(row['destination']))
    
    # Create dictionaries for tract data
    print("Creating tract data dictionaries...")
    tract_populations = {}
    tract_coords = {}
    
    # Populate tract data from the dataframe
    for _, row in df.iterrows():
        source_tract = str(row['source'])
        dest_tract = str(row['destination'])
        
        # Add source tract data
        if source_tract not in tract_populations and not pd.isna(row['source_population']):
            tract_populations[source_tract] = row['source_population']
        
        if source_tract not in tract_coords and not pd.isna(row['source_latitude']) and not pd.isna(row['source_longitude']):
            tract_coords[source_tract] = {
                'latitude': row['source_latitude'],
                'longitude': row['source_longitude']
            }
            
        # Add destination tract data
        if dest_tract not in tract_populations and not pd.isna(row['destination_population']):
            tract_populations[dest_tract] = row['destination_population']
        
        if dest_tract not in tract_coords and not pd.isna(row['destination_latitude']) and not pd.isna(row['destination_longitude']):
            tract_coords[dest_tract] = {
                'latitude': row['destination_latitude'],
                'longitude': row['destination_longitude']
            }
    
    # Create arrays for KD-Tree
    print("Building KD-Tree for spatial queries...")
    tract_ids = []
    coords = []
    
    for tract_id, coord_data in tract_coords.items():
        tract_ids.append(tract_id)
        # Convert to radians for more accurate distance calculations on a sphere
        lat_rad = radians(coord_data['latitude'])
        lon_rad = radians(coord_data['longitude'])
        # Project to 3D coordinates on a unit sphere
        x = cos(lat_rad) * cos(lon_rad)
        y = cos(lat_rad) * sin(lon_rad)
        z = sin(lat_rad)
        coords.append([x, y, z])
    
    # Create KD-Tree
    tree = cKDTree(coords)
    
    # Initialize new columns
    df['population_within_circle'] = 0.0
    df['missing_population_tracts'] = 0
    
    # Process in batches to reduce memory pressure
    print("Processing data in batches...")
    batch_size = 1000
    total_rows = len(df)
    num_batches = (total_rows + batch_size - 1) // batch_size
    
    for batch_idx in range(num_batches):
        start_idx = batch_idx * batch_size
        end_idx = min((batch_idx + 1) * batch_size, total_rows)
        
        print(f"Processing batch {batch_idx+1}/{num_batches} (rows {start_idx}-{end_idx})...")
        
        # Process each row in the batch
        for idx in range(start_idx, end_idx):
            row = df.iloc[idx]
            source_tract = str(row['source'])
            dest_tract = str(row['destination'])
            distance = row['source_destination_distance']
            
            # Skip if distance is invalid
            if pd.isna(distance) or distance <= 0:
                continue
            
            # Get destination coordinates
            dest_lat = row['destination_latitude']
            dest_lon = row['destination_longitude']
            
            # Skip if coordinates are invalid
            if pd.isna(dest_lat) or pd.isna(dest_lon):
                continue
            
            # Convert destination coordinates to 3D unit vector
            dest_lat_rad = radians(dest_lat)
            dest_lon_rad = radians(dest_lon)
            dest_x = cos(dest_lat_rad) * cos(dest_lon_rad)
            dest_y = cos(dest_lat_rad) * sin(dest_lon_rad)
            dest_z = sin(dest_lat_rad)
            dest_point = [dest_x, dest_y, dest_z]
            
            # Find all points within the radius
            # The distance on a unit sphere is related to the great circle distance by:
            # chord_length = 2 * sin(angular_distance / 2)
            # where angular_distance = great_circle_distance / earth_radius
            # So we need to convert our distance in km to a chord length
            angular_distance = distance / 6371.0  # distance / earth_radius
            chord_length = 2 * sin(angular_distance / 2)
            
            # Query the KD-Tree
            indices = tree.query_ball_point(dest_point, chord_length)
            
            population_within_circle = 0.0
            missing_population_tracts = 0
            
            # Process the results
            for i in indices:
                tract_id = tract_ids[i]
                
                # Skip source and destination tracts
                if tract_id == source_tract or tract_id == dest_tract:
                    continue
                
                # Get tract population
                if tract_id in tract_populations:
                    tract_population = tract_populations[tract_id]
                    # Add to population within circle
                    population_within_circle += tract_population
                else:
                    # Increment missing data counter
                    missing_population_tracts += 1
            
            # Save the calculated values
            df.at[idx, 'population_within_circle'] = population_within_circle
            df.at[idx, 'missing_population_tracts'] = missing_population_tracts
    
    # Save the result to a new CSV file
    print(f"Saving result to {output_file}...")
    df.to_csv(output_file, index=False)
    
    end_time = time.time()
    elapsed_time = end_time - start_time
    print(f"Population within Circle calculation completed in {elapsed_time:.2f} seconds.")
    print(f"Output saved to: {output_file}")

if __name__ == "__main__":
    calculate_population_within_circle_optimized()
