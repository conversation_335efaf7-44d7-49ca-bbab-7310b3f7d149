import pandas as pd
import os
from math import radians, sin, cos, sqrt, atan2

def haversine_distance(lat1, lon1, lat2, lon2):
    """
    Calculate the great circle distance between two points 
    on the earth (specified in decimal degrees)
    
    Returns:
        float: Distance in kilometers
    """
    # Convert latitude and longitude from degrees to radians
    lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
    
    # Haversine formula
    dlon = lon2 - lon1
    dlat = lat2 - lat1
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * atan2(sqrt(a), sqrt(1-a))
    r = 6371  # Radius of Earth in kilometers
    
    return r * c  # Distance in kilometers

def calculate_population_within_circle():
    """
    Calculates the 'Population within Circle' column for the cleaned dataset.
    
    The Population_within_Circle represents the total population of all tracts 
    that fall within the circle centered at the destination tract and with a radius 
    equal to the distance between the source and destination tracts, excluding the 
    population of the source and destination tracts themselves.
    
    Steps:
    1. Read the cleaned dataset
    2. Calculate the distance between source and destination for each row
    3. For each source-destination pair, identify all tracts within the circle
    4. Sum the population of all tracts within the circle (excluding source and destination)
    5. Save the result to a new CSV file
    """
    print("Starting Population within Circle calculation...")
    
    # Input and output file paths
    input_file = 'NYC_cleaned-2.csv'
    output_dir = 'population_analysis'
    output_file = os.path.join(output_dir, 'NYC_with_population_circle.csv')
    
    # Read the cleaned dataset
    print(f"Reading cleaned dataset from {input_file}...")
    df = pd.read_csv(input_file)
    
    # Standardize column names (convert to lowercase for consistency)
    df.columns = [col.lower().replace('-', '_') for col in df.columns]
    
    # Calculate the distance between source and destination for each row
    print("Calculating distances between source and destination tracts...")
    df['source_destination_distance'] = df.apply(
        lambda row: haversine_distance(
            row['source_latitude'], 
            row['source_longitude'],
            row['destination_latitude'], 
            row['destination_longitude']
        ), 
        axis=1
    )
    
    # Create a dictionary of all tracts and their populations
    print("Creating tract population dictionary...")
    tract_populations = {}
    for _, row in df.iterrows():
        source_tract = str(row['source'])
        dest_tract = str(row['destination'])
        
        # Add source tract population if not already in dictionary
        if source_tract not in tract_populations and not pd.isna(row['source_population']):
            tract_populations[source_tract] = row['source_population']
            
        # Add destination tract population if not already in dictionary
        if dest_tract not in tract_populations and not pd.isna(row['destination_population']):
            tract_populations[dest_tract] = row['destination_population']
    
    # Create a dictionary of tract coordinates
    print("Creating tract coordinates dictionary...")
    tract_coords = {}
    for _, row in df.iterrows():
        source_tract = str(row['source'])
        dest_tract = str(row['destination'])
        
        # Add source tract coordinates if not already in dictionary
        if source_tract not in tract_coords and not pd.isna(row['source_latitude']) and not pd.isna(row['source_longitude']):
            tract_coords[source_tract] = {
                'latitude': row['source_latitude'],
                'longitude': row['source_longitude']
            }
            
        # Add destination tract coordinates if not already in dictionary
        if dest_tract not in tract_coords and not pd.isna(row['destination_latitude']) and not pd.isna(row['destination_longitude']):
            tract_coords[dest_tract] = {
                'latitude': row['destination_latitude'],
                'longitude': row['destination_longitude']
            }
    
    # Initialize new columns
    df['population_within_circle'] = 0
    df['missing_population_tracts'] = 0
    
    # Calculate population within circle for each row
    print("Calculating population within circle for each row...")
    total_rows = len(df)
    
    for idx, row in df.iterrows():
        if idx % 1000 == 0:
            print(f"Processing row {idx}/{total_rows}...")
            
        source_tract = str(row['source'])
        dest_tract = str(row['destination'])
        distance = row['source_destination_distance']
        
        # Skip if distance is invalid
        if pd.isna(distance) or distance <= 0:
            continue
        
        # Get destination coordinates
        dest_lat = row['destination_latitude']
        dest_lon = row['destination_longitude']
        
        # Skip if coordinates are invalid
        if pd.isna(dest_lat) or pd.isna(dest_lon):
            continue
        
        population_within_circle = 0
        missing_population_tracts = 0
        
        # Check each tract to see if it's within the circle
        for tract, coords in tract_coords.items():
            # Skip source and destination tracts
            if tract == source_tract or tract == dest_tract:
                continue
            
            tract_lat = coords['latitude']
            tract_lon = coords['longitude']
            
            # Calculate distance from destination to this tract
            tract_distance = haversine_distance(dest_lat, dest_lon, tract_lat, tract_lon)
            
            # If distance is less than or equal to the radius (source-destination distance),
            # the tract is within the circle
            if tract_distance <= distance:
                # Get tract population
                if tract in tract_populations:
                    tract_population = tract_populations[tract]
                    # Add to population within circle
                    population_within_circle += tract_population
                else:
                    # Increment missing data counter
                    missing_population_tracts += 1
        
        # Save the calculated values
        df.at[idx, 'population_within_circle'] = population_within_circle
        df.at[idx, 'missing_population_tracts'] = missing_population_tracts
    
    # Save the result to a new CSV file
    print(f"Saving result to {output_file}...")
    df.to_csv(output_file, index=False)
    
    print("Population within Circle calculation completed.")
    print(f"Output saved to: {output_file}")

if __name__ == "__main__":
    calculate_population_within_circle()
