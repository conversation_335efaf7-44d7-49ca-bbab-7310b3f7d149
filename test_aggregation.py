#!/usr/bin/env python3
"""
Test script to validate the aggregation logic works correctly.
This creates sample data and tests the aggregation functionality.
"""

import pandas as pd
import os

def create_test_data():
    """Create sample test data to validate aggregation logic."""
    
    # Create sample data with duplicate Source-Destination pairs
    test_data = [
        # Same Source-Destination pair across different time periods
        {'Source': '36061000100', 'Destination': '36061000200', 'AggFlow': 10, 
         'Source_Latitude': 40.7128, 'Source_Longitude': -74.0060,
         'Destination_Latitude': 40.7589, 'Destination_Longitude': -73.9851,
         'Source_Population': 1000, 'Destination_Population': 1500,
         'Source_Coordinate_Source': 'census'},
        
        {'Source': '36061000100', 'Destination': '36061000200', 'AggFlow': 15,
         'Source_Latitude': 40.7128, 'Source_Longitude': -74.0060,
         'Destination_Latitude': 40.7589, 'Destination_Longitude': -73.9851,
         'Source_Population': 1000, 'Destination_Population': 1500,
         'Source_Coordinate_Source': 'census'},
        
        # Different Source-Destination pair
        {'Source': '36061000300', 'Destination': '36061000400', 'AggFlow': 8,
         'Source_Latitude': 40.7831, 'Source_Longitude': -73.9712,
         'Destination_Latitude': 40.7505, 'Destination_Longitude': -73.9934,
         'Source_Population': 800, 'Destination_Population': 1200,
         'Source_Coordinate_Source': 'tessellation'},
        
        # Same as first pair again (should be aggregated)
        {'Source': '36061000100', 'Destination': '36061000200', 'AggFlow': 5,
         'Source_Latitude': 40.7128, 'Source_Longitude': -74.0060,
         'Destination_Latitude': 40.7589, 'Destination_Longitude': -73.9851,
         'Source_Population': 1000, 'Destination_Population': 1500,
         'Source_Coordinate_Source': 'census'}
    ]
    
    return pd.DataFrame(test_data)

def test_aggregation_logic():
    """Test the aggregation logic with sample data."""
    
    print("Creating test data...")
    test_df = create_test_data()
    print(f"Original data has {len(test_df)} rows")
    print("\nOriginal data:")
    print(test_df[['Source', 'Destination', 'AggFlow']])
    
    print("\nPerforming aggregation...")
    
    # Define aggregation functions for each column
    agg_functions = {
        'AggFlow': 'sum',  # Sum flows across time periods
        'Source_Latitude': 'first',  # Take first non-null value
        'Source_Longitude': 'first',
        'Destination_Latitude': 'first',
        'Destination_Longitude': 'first',
        'Source_Population': 'first',
        'Destination_Population': 'first',
        'Source_Coordinate_Source': 'first'  # Preserve coordinate source tracking
    }
    
    # Group by Source-Destination pairs and aggregate
    aggregated_df = test_df.groupby(['Source', 'Destination']).agg(agg_functions).reset_index()
    
    print(f"Aggregated data has {len(aggregated_df)} unique Source-Destination pairs")
    print("\nAggregated data:")
    print(aggregated_df[['Source', 'Destination', 'AggFlow', 'Source_Coordinate_Source']])
    
    # Validate results
    expected_results = {
        ('36061000100', '36061000200'): 30,  # 10 + 15 + 5 = 30
        ('36061000300', '36061000400'): 8    # 8
    }
    
    print("\nValidation:")
    for _, row in aggregated_df.iterrows():
        source = row['Source']
        destination = row['Destination']
        agg_flow = row['AggFlow']
        expected_flow = expected_results.get((source, destination))
        
        if expected_flow == agg_flow:
            print(f"✅ {source} -> {destination}: {agg_flow} (correct)")
        else:
            print(f"❌ {source} -> {destination}: {agg_flow} (expected {expected_flow})")
    
    print("\n✅ Aggregation logic test completed successfully!")
    return aggregated_df

if __name__ == "__main__":
    test_aggregation_logic()
