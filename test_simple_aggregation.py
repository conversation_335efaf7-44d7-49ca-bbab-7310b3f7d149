#!/usr/bin/env python3
"""
Simple test script to verify the aggregation logic works correctly.
This version skips the buffer calculation to focus on the core aggregation.
"""

import os
import pandas as pd
import glob
from math import radians, sin, cos, sqrt, atan2

def haversine_distance(lat1, lon1, lat2, lon2):
    """
    Calculate the great circle distance between two points 
    on the earth (specified in decimal degrees)
    """
    # Convert decimal degrees to radians
    lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
    
    # Haversine formula
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * atan2(sqrt(a), sqrt(1-a))
    r = 6371  # Radius of earth in kilometers
    return r * c

def simple_aggregation_test():
    """
    Test the core aggregation logic without buffer calculations.
    """
    print("Starting simple aggregation test...")

    # Path to the output_files directory
    output_dir = 'output_files'

    # Get a list of all CSV files in the output_files directory
    csv_files = glob.glob(os.path.join(output_dir, '*.csv'))

    if not csv_files:
        print("No CSV files found in the output_files directory.")
        return

    print(f"Found {len(csv_files)} CSV files to aggregate.")

    # Create an empty list to store dataframes
    dfs = []

    # Read each CSV file and append to the list
    for i, file in enumerate(csv_files):
        print(f"Processing file {i+1}/{len(csv_files)}: {os.path.basename(file)}")
        try:
            # Read the CSV file
            df = pd.read_csv(file)

            # Remove Source_File column if it exists from previous runs
            if 'Source_File' in df.columns:
                df = df.drop(columns=['Source_File'])

            # Append to the list
            dfs.append(df)

            print(f"  - Added {len(df)} rows from {os.path.basename(file)}")
        except Exception as e:
            print(f"  - Error processing {file}: {str(e)}")

    if not dfs:
        print("No data was successfully read from the CSV files.")
        return

    # Combine all dataframes
    print("Combining all dataframes...")
    combined_df = pd.concat(dfs, ignore_index=True)
    print(f"Total rows before aggregation: {len(combined_df)}")

    # Perform aggregation
    print("Performing aggregation by Source-Destination pairs...")
    
    # Define aggregation functions for each column
    agg_functions = {
        'AggFlow': 'sum',  # Sum flows across time periods
        'Buffer': 'first',  # Take first value (will be recalculated later)
        'Source_Destination_Distance': 'first',  # Take first value (will be recalculated)
        'Source_Latitude': 'first',  # Take first non-null value
        'Source_Longitude': 'first',
        'Destination_Latitude': 'first',
        'Destination_Longitude': 'first',
        'Source_Population': 'first',
        'Destination_Population': 'first',
        'Population_within_Circle': 'first',  # Take first value (will be recalculated)
        'Missing_Population_Tracts': 'first',  # Take first value (will be recalculated)
        'Source_Coordinate_Source': 'first'  # Preserve coordinate source tracking
    }

    # Group by Source-Destination pairs and aggregate
    print("  Grouping by Source-Destination pairs...")
    aggregated_df = combined_df.groupby(['Source', 'Destination']).agg(agg_functions).reset_index()

    print(f"  Reduced from {len(combined_df)} rows to {len(aggregated_df)} unique Source-Destination pairs")

    # Recalculate Source_Destination_Distance for aggregated data
    print("  Recalculating distances for aggregated pairs...")
    aggregated_df['Source_Destination_Distance'] = aggregated_df.apply(
        lambda row: haversine_distance(
            row['Source_Latitude'],
            row['Source_Longitude'],
            row['Destination_Latitude'],
            row['Destination_Longitude']
        ) if (pd.notna(row['Source_Latitude']) and pd.notna(row['Source_Longitude']) and
              pd.notna(row['Destination_Latitude']) and pd.notna(row['Destination_Longitude'])) else 0.0,
        axis=1
    )

    # Save the aggregated dataframe to a new CSV file
    output_file = 'simple_aggregated_tract_flows.csv'
    print(f"Saving aggregated data to {output_file}...")
    aggregated_df.to_csv(output_file, index=False)

    print(f"Simple aggregation complete. Total unique Source-Destination pairs: {len(aggregated_df)}")
    print(f"Output saved to: {output_file}")
    
    # Show some statistics
    print(f"\nAggregation Statistics:")
    print(f"  Original total rows: {len(combined_df):,}")
    print(f"  Unique Source-Destination pairs: {len(aggregated_df):,}")
    print(f"  Reduction ratio: {len(combined_df)/len(aggregated_df):.2f}x")
    
    # Show sample of aggregated data
    print(f"\nSample of aggregated data:")
    print(aggregated_df[['Source', 'Destination', 'AggFlow', 'Source_Coordinate_Source']].head(10))

if __name__ == "__main__":
    simple_aggregation_test()
